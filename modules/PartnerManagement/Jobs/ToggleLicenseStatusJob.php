<?php

namespace Modules\PartnerManagement\Jobs;

use Exception;
use Illuminate\Support\Facades\DB;
use Modules\PartnerManagement\Models\PartnerLicense;

class ToggleLicenseStatusJob
{
    private $licenseId;
    private $action; // 'lock' hoặc 'unlock'
    private $companyId;

    public function __construct($licenseId, $action, $companyId)
    {
        $this->licenseId = $licenseId;
        $this->action = $action;
        $this->companyId = $companyId;
    }

    public function handle()
    {
        try {
            DB::beginTransaction();

            // Xác định status hiện tại và status mới dựa trên action
            if ($this->action === 'lock') {
                $currentStatus = PartnerLicense::STATUS_ACTIVATED;
                $newStatus = PartnerLicense::STATUS_PENDING;
                $message = 'Khóa giấy phép thành công!';
                $errorMessage = 'Không thể khóa giấy phép đã hết hạn!';
            } elseif ($this->action === 'unlock') {
                $currentStatus = PartnerLicense::STATUS_PENDING;
                $newStatus = PartnerLicense::STATUS_ACTIVATED;
                $message = 'Mở khóa giấy phép thành công!';
                $errorMessage = 'Không thể mở khóa giấy phép đã hết hạn!';
            } else {
                throw new Exception('Action không hợp lệ. Chỉ chấp nhận "lock" hoặc "unlock".');
            }

            // Tìm license với status hiện tại
            $license = PartnerLicense::where('company_id', $this->companyId)
                ->where('status', $currentStatus)
                ->findOrFail($this->licenseId);

            // Kiểm tra xem license có còn hạn không
            if ($license->expired_at && $license->expired_at->isPast()) {
                throw new Exception($errorMessage);
            }

            // Cập nhật status
            $license->status = $newStatus;
            $license->save();

            // Clear cache
            $this->clearLicenseCache($this->licenseId, $license->project_unit_ref);

            DB::commit();

            return [
                'success' => true,
                'message' => $message,
                'license' => $license
            ];

        } catch (Exception $e) {
            DB::rollBack();
            return [
                'success' => false,
                'message' => $e->getMessage(),
                'license' => null
            ];
        }
    }

    /**
     * Clear cache cho license
     */
    private function clearLicenseCache($licenseId, $projectUnitRef = null)
    {
        \Cache::forget('partner_license_activities_' . $licenseId);

        if ($projectUnitRef) {
            \Cache::forget('partner_license_unit_' . $projectUnitRef);
        }
    }
}
